# Guía de Pruebas - Interfaz de Administración

## Requisitos Previos

1. **Backend ejecutándose**: El servidor Spring Boot debe estar corriendo en `http://localhost:8080`
2. **Usuario administrador**: Debe existir al menos un usuario con rol `ADMIN` en la base de datos

## Crear Usuario Administrador (SQL)

Si no tienes un usuario administrador, puedes crear uno ejecutando este SQL en tu base de datos:

```sql
-- Insertar usuario administrador (ajusta según tu esquema)
INSERT INTO users (email, username, password, first_name, last_name, role, enabled, created_at, updated_at) 
VALUES (
    '<EMAIL>', 
    'admin', 
    '$2a$10$ejemplo_hash_bcrypt', -- Reemplazar con hash BCrypt real
    'Administrador', 
    'Sistema', 
    'ADMIN', 
    true, 
    NOW(), 
    NOW()
);
```

## Pruebas de Funcionalidad

### 1. Login
- [ ] Abrir `http://localhost:5173`
- [ ] Verificar que aparece el formulario de login
- [ ] Probar login con credenciales incorrectas (debe mostrar error)
- [ ] Probar login con usuario no-admin (debe mostrar "Acceso denegado")
- [ ] Login exitoso con usuario admin (debe mostrar panel)

### 2. Panel de Administración

#### Estadísticas
- [ ] Verificar que se muestran las 6 métricas:
  - Total Usuarios
  - Usuarios Activos
  - Usuarios Inactivos
  - Administradores
  - Usuarios Regulares
  - Tokens Push

#### Lista de Usuarios
- [ ] Cambiar a pestaña "Usuarios"
- [ ] Verificar que se cargan los usuarios
- [ ] Probar paginación (si hay más de 20 usuarios)
- [ ] Probar acciones:
  - [ ] Activar/Desactivar usuario
  - [ ] Cerrar sesiones de usuario
  - [ ] Eliminar usuario (con confirmación)

#### Crear Usuario
- [ ] Cambiar a pestaña "Crear Usuario"
- [ ] Llenar formulario completo
- [ ] Verificar validaciones (email, contraseña mínima)
- [ ] Crear usuario exitosamente
- [ ] Verificar que aparece en la lista

### 3. Diseño y UX
- [ ] Verificar diseño monocromo (solo blanco y negro)
- [ ] Probar responsividad en móvil
- [ ] Verificar estados hover en botones
- [ ] Probar navegación por teclado
- [ ] Verificar que los mensajes de error son claros

### 4. Logout
- [ ] Hacer click en "Cerrar Sesión"
- [ ] Verificar que regresa al login
- [ ] Verificar que no se puede acceder al panel sin autenticación

## Problemas Comunes

### Error de CORS
Si ves errores de CORS, asegúrate de que el backend tenga configurado:
```java
@CrossOrigin(origins = "http://localhost:5173")
```

### Error 401 Unauthorized
- Verificar que el usuario existe y tiene rol ADMIN
- Verificar que la contraseña está correctamente hasheada con BCrypt

### Error de conexión
- Verificar que el backend está corriendo en puerto 8080
- Verificar que la URL en `src/services/api.ts` es correcta

## Datos de Prueba

Para pruebas más completas, puedes crear usuarios adicionales:

```sql
-- Usuario regular
INSERT INTO users (email, username, password, first_name, last_name, role, enabled, created_at, updated_at) 
VALUES ('<EMAIL>', 'testuser', '$2a$10$hash', 'Usuario', 'Prueba', 'USER', true, NOW(), NOW());

-- Usuario deshabilitado
INSERT INTO users (email, username, password, first_name, last_name, role, enabled, created_at, updated_at) 
VALUES ('<EMAIL>', 'disabled', '$2a$10$hash', 'Usuario', 'Deshabilitado', 'USER', false, NOW(), NOW());
```

## Verificación Final

✅ **Login funciona correctamente**
✅ **Panel muestra estadísticas reales**
✅ **CRUD de usuarios operativo**
✅ **Diseño minimalista monocromo**
✅ **Responsive en móvil**
✅ **Manejo de errores apropiado**
