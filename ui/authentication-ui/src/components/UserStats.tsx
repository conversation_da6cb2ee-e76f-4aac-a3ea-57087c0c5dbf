import React from 'react';
import { UserStats as UserStatsType } from '../types/api';

interface UserStatsProps {
  stats: UserStatsType;
}

const UserStats: React.FC<UserStatsProps> = ({ stats }) => {
  return (
    <div className="user-stats">
      <h2 className="stats-title">Estadísticas del Sistema</h2>
      
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-number">{stats.totalUsers}</div>
          <div className="stat-label">Total Usuarios</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-number">{stats.enabledUsers}</div>
          <div className="stat-label">Usuarios Activos</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-number">{stats.disabledUsers}</div>
          <div className="stat-label">Usuarios Inactivos</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-number">{stats.adminUsers}</div>
          <div className="stat-label">Administradores</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-number">{stats.regularUsers}</div>
          <div className="stat-label">Usuarios Regulares</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-number">{stats.totalPushTokens}</div>
          <div className="stat-label">Tokens Push</div>
        </div>
      </div>
    </div>
  );
};

export default UserStats;
