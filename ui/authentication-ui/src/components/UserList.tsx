import React, { useState } from 'react';
import { UserProfileResponse, PagedResponse, Role } from '../types/api';
import { apiService } from '../services/api';

interface UserListProps {
  users: PagedResponse<UserProfileResponse>;
  onUserUpdated: () => void;
  onLoadUsers: (page?: number, size?: number) => void;
}

const UserList: React.FC<UserListProps> = ({ users, onUserUpdated, onLoadUsers }) => {
  const [loading, setLoading] = useState<number | null>(null);
  const [error, setError] = useState('');

  const handleToggleEnabled = async (userId: number, currentEnabled: boolean = true) => {
    setLoading(userId);
    setError('');
    try {
      await apiService.setUserEnabled(userId, !currentEnabled);
      onUserUpdated();
    } catch (err: any) {
      setError(err.message || 'Error actualizando usuario');
    } finally {
      setLoading(null);
    }
  };

  const handleInvalidateSessions = async (userId: number) => {
    setLoading(userId);
    setError('');
    try {
      await apiService.invalidateUserSessions(userId);
      alert('Sesiones invalidadas exitosamente');
    } catch (err: any) {
      setError(err.message || 'Error invalidando sesiones');
    } finally {
      setLoading(null);
    }
  };

  const handleDeleteUser = async (userId: number, userEmail: string) => {
    if (!confirm(`¿Está seguro de eliminar el usuario ${userEmail}?`)) {
      return;
    }

    setLoading(userId);
    setError('');
    try {
      await apiService.deleteUser(userId);
      onUserUpdated();
    } catch (err: any) {
      setError(err.message || 'Error eliminando usuario');
    } finally {
      setLoading(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handlePageChange = (newPage: number) => {
    onLoadUsers(newPage, users.size);
  };

  return (
    <div className="user-list">
      <div className="user-list-header">
        <h2 className="list-title">Gestión de Usuarios</h2>
        <div className="list-info">
          Total: {users.totalElements} usuarios
        </div>
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      <div className="user-table">
        <div className="table-header">
          <div className="table-cell">Email</div>
          <div className="table-cell">Nombre</div>
          <div className="table-cell">Rol</div>
          <div className="table-cell">Estado</div>
          <div className="table-cell">Creado</div>
          <div className="table-cell">Acciones</div>
        </div>

        {users.content.map((user) => (
          <div key={user.id} className="table-row">
            <div className="table-cell">{user.email}</div>
            <div className="table-cell">
              {user.firstName && user.lastName 
                ? `${user.firstName} ${user.lastName}` 
                : user.username || '-'}
            </div>
            <div className="table-cell">
              <span className={`role-badge ${user.role.toLowerCase()}`}>
                {user.role}
              </span>
            </div>
            <div className="table-cell">
              <span className={`status-badge ${user.enabled ? 'enabled' : 'disabled'}`}>
                {user.enabled ? 'Activo' : 'Inactivo'}
              </span>
            </div>
            <div className="table-cell">{formatDate(user.createdAt)}</div>
            <div className="table-cell">
              <div className="action-buttons">
                <button
                  onClick={() => handleToggleEnabled(user.id, user.enabled)}
                  disabled={loading === user.id}
                  className={`action-button ${user.enabled ? 'disable' : 'enable'}`}
                >
                  {user.enabled ? 'Desactivar' : 'Activar'}
                </button>
                <button
                  onClick={() => handleInvalidateSessions(user.id)}
                  disabled={loading === user.id}
                  className="action-button secondary"
                >
                  Cerrar Sesiones
                </button>
                <button
                  onClick={() => handleDeleteUser(user.id, user.email)}
                  disabled={loading === user.id}
                  className="action-button danger"
                >
                  Eliminar
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {users.totalPages > 1 && (
        <div className="pagination">
          <button
            onClick={() => handlePageChange(users.page - 1)}
            disabled={users.first}
            className="pagination-button"
          >
            Anterior
          </button>
          <span className="pagination-info">
            Página {users.page + 1} de {users.totalPages}
          </span>
          <button
            onClick={() => handlePageChange(users.page + 1)}
            disabled={users.last}
            className="pagination-button"
          >
            Siguiente
          </button>
        </div>
      )}
    </div>
  );
};

export default UserList;
