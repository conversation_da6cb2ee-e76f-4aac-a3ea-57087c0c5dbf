import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { apiService } from '../services/api';
import { UserProfileResponse, PagedResponse, UserStats, Role } from '../types/api';
import UserList from './UserList';
import UserForm from './UserForm';
import UserStatsComponent from './UserStats';

type View = 'stats' | 'users' | 'create';

const AdminPanel: React.FC = () => {
  const { user, logout } = useAuth();
  const [currentView, setCurrentView] = useState<View>('stats');
  const [users, setUsers] = useState<PagedResponse<UserProfileResponse> | null>(null);
  const [stats, setStats] = useState<UserStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (currentView === 'stats') {
      loadStats();
    } else if (currentView === 'users') {
      loadUsers();
    }
  }, [currentView]);

  const loadStats = async () => {
    setLoading(true);
    setError('');
    try {
      const statsData = await apiService.getUserStats();
      setStats(statsData);
    } catch (err: any) {
      setError(err.message || 'Error cargando estadísticas');
    } finally {
      setLoading(false);
    }
  };

  const loadUsers = async (page = 0, size = 20) => {
    setLoading(true);
    setError('');
    try {
      const usersData = await apiService.getUsers({ page, size });
      setUsers(usersData);
    } catch (err: any) {
      setError(err.message || 'Error cargando usuarios');
    } finally {
      setLoading(false);
    }
  };

  const handleUserCreated = () => {
    setCurrentView('users');
    loadUsers();
  };

  const handleUserUpdated = () => {
    loadUsers();
    if (currentView === 'stats') {
      loadStats();
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  return (
    <div className="admin-panel">
      <header className="admin-header">
        <div className="admin-header-content">
          <h1 className="admin-title">Panel de Administración</h1>
          <div className="admin-user-info">
            <span className="user-name">
              {user?.firstName || user?.email} ({user?.role})
            </span>
            <button onClick={handleLogout} className="logout-button">
              Cerrar Sesión
            </button>
          </div>
        </div>
      </header>

      <nav className="admin-nav">
        <button
          onClick={() => setCurrentView('stats')}
          className={`nav-button ${currentView === 'stats' ? 'active' : ''}`}
        >
          Estadísticas
        </button>
        <button
          onClick={() => setCurrentView('users')}
          className={`nav-button ${currentView === 'users' ? 'active' : ''}`}
        >
          Usuarios
        </button>
        <button
          onClick={() => setCurrentView('create')}
          className={`nav-button ${currentView === 'create' ? 'active' : ''}`}
        >
          Crear Usuario
        </button>
      </nav>

      <main className="admin-content">
        {error && (
          <div className="error-message">
            {error}
          </div>
        )}

        {loading && (
          <div className="loading-message">
            Cargando...
          </div>
        )}

        {currentView === 'stats' && stats && !loading && (
          <UserStatsComponent stats={stats} />
        )}

        {currentView === 'users' && users && !loading && (
          <UserList 
            users={users} 
            onUserUpdated={handleUserUpdated}
            onLoadUsers={loadUsers}
          />
        )}

        {currentView === 'create' && (
          <UserForm 
            onUserCreated={handleUserCreated}
            onCancel={() => setCurrentView('users')}
          />
        )}
      </main>
    </div>
  );
};

export default AdminPanel;
