/* Reset y variables globales */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --color-white: #ffffff;
  --color-black: #000000;
  --color-gray-50: #fafafa;
  --color-gray-100: #f5f5f5;
  --color-gray-200: #e5e5e5;
  --color-gray-300: #d4d4d4;
  --color-gray-400: #a3a3a3;
  --color-gray-500: #737373;
  --color-gray-600: #525252;
  --color-gray-700: #404040;
  --color-gray-800: #262626;
  --color-gray-900: #171717;

  --border-radius: 4px;
  --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 4px 6px rgba(0, 0, 0, 0.1);

  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

body {
  margin: 0;
  min-height: 100vh;
  background-color: var(--color-white);
  color: var(--color-black);
  font-size: 14px;
}

/* Componentes base */
.form-input,
.form-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--color-gray-300);
  border-radius: var(--border-radius);
  font-size: 14px;
  background-color: var(--color-white);
  color: var(--color-black);
  transition: border-color 0.2s;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: var(--color-black);
}

.form-input:disabled,
.form-select:disabled {
  background-color: var(--color-gray-100);
  color: var(--color-gray-500);
  cursor: not-allowed;
}

.form-label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: var(--color-black);
  font-size: 13px;
}

.form-group {
  margin-bottom: 16px;
}

.form-checkbox {
  margin-right: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  font-size: 14px;
  cursor: pointer;
}

/* Botones */
.btn {
  padding: 8px 16px;
  border: 1px solid var(--color-gray-300);
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  background-color: var(--color-white);
  color: var(--color-black);
}

.btn:hover:not(:disabled) {
  background-color: var(--color-gray-50);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--color-black);
  color: var(--color-white);
  border-color: var(--color-black);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-gray-800);
}

.btn-danger {
  background-color: var(--color-white);
  color: var(--color-black);
  border-color: var(--color-black);
}

.btn-danger:hover:not(:disabled) {
  background-color: var(--color-black);
  color: var(--color-white);
}

/* Mensajes */
.error-message {
  padding: 8px 12px;
  background-color: var(--color-gray-100);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--border-radius);
  color: var(--color-black);
  font-size: 13px;
  margin-bottom: 16px;
}

.loading-message {
  text-align: center;
  padding: 32px;
  color: var(--color-gray-500);
  font-size: 14px;
}

/* Login */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-white);
}

.login-card {
  width: 100%;
  max-width: 400px;
  padding: 32px;
  border: 1px solid var(--color-gray-200);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
}

.login-title {
  font-size: 24px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 32px;
  color: var(--color-black);
}

.login-form {
  display: flex;
  flex-direction: column;
}

.login-button {
  width: 100%;
  padding: 12px;
  background-color: var(--color-black);
  color: var(--color-white);
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.login-button:hover:not(:disabled) {
  background-color: var(--color-gray-800);
}

.login-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Admin Panel */
.admin-panel {
  min-height: 100vh;
  background-color: var(--color-white);
}

.admin-header {
  border-bottom: 1px solid var(--color-gray-200);
  background-color: var(--color-white);
}

.admin-header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-black);
}

.admin-user-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-name {
  font-size: 14px;
  color: var(--color-gray-600);
}

.logout-button {
  padding: 6px 12px;
  background-color: var(--color-white);
  color: var(--color-black);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--border-radius);
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
}

.logout-button:hover {
  background-color: var(--color-gray-50);
}

.admin-nav {
  border-bottom: 1px solid var(--color-gray-200);
  background-color: var(--color-white);
}

.admin-nav {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  gap: 0;
}

.nav-button {
  padding: 12px 16px;
  background-color: transparent;
  color: var(--color-gray-600);
  border: none;
  border-bottom: 2px solid transparent;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.nav-button:hover {
  color: var(--color-black);
}

.nav-button.active {
  color: var(--color-black);
  border-bottom-color: var(--color-black);
}

.admin-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

/* User Stats */
.user-stats {
  margin-bottom: 32px;
}

.stats-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--color-black);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  padding: 20px;
  border: 1px solid var(--color-gray-200);
  border-radius: var(--border-radius);
  text-align: center;
  background-color: var(--color-white);
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: var(--color-black);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 13px;
  color: var(--color-gray-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* User List */
.user-list {
  margin-bottom: 32px;
}

.user-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.list-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-black);
}

.list-info {
  font-size: 13px;
  color: var(--color-gray-600);
}

.user-table {
  border: 1px solid var(--color-gray-200);
  border-radius: var(--border-radius);
  overflow: hidden;
  background-color: var(--color-white);
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1fr 1fr 1.5fr 2fr;
  background-color: var(--color-gray-50);
  border-bottom: 1px solid var(--color-gray-200);
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1fr 1fr 1.5fr 2fr;
  border-bottom: 1px solid var(--color-gray-100);
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 12px;
  font-size: 13px;
  display: flex;
  align-items: center;
}

.table-header .table-cell {
  font-weight: 600;
  color: var(--color-gray-700);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 11px;
}

.role-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.role-badge.admin {
  background-color: var(--color-black);
  color: var(--color-white);
}

.role-badge.user {
  background-color: var(--color-gray-200);
  color: var(--color-gray-700);
}

.status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.enabled {
  background-color: var(--color-gray-100);
  color: var(--color-black);
}

.status-badge.disabled {
  background-color: var(--color-gray-200);
  color: var(--color-gray-600);
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-button {
  padding: 4px 8px;
  border: 1px solid var(--color-gray-300);
  border-radius: var(--border-radius);
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  background-color: var(--color-white);
  color: var(--color-black);
}

.action-button:hover:not(:disabled) {
  background-color: var(--color-gray-50);
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-button.danger {
  border-color: var(--color-black);
}

.action-button.danger:hover:not(:disabled) {
  background-color: var(--color-black);
  color: var(--color-white);
}

.action-button.secondary {
  background-color: var(--color-gray-100);
  border-color: var(--color-gray-300);
}

.action-button.enable {
  background-color: var(--color-black);
  color: var(--color-white);
  border-color: var(--color-black);
}

.action-button.disable {
  background-color: var(--color-gray-200);
  color: var(--color-gray-700);
  border-color: var(--color-gray-300);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 16px;
}

.pagination-button {
  padding: 8px 16px;
  border: 1px solid var(--color-gray-300);
  border-radius: var(--border-radius);
  background-color: var(--color-white);
  color: var(--color-black);
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
}

.pagination-button:hover:not(:disabled) {
  background-color: var(--color-gray-50);
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  font-size: 13px;
  color: var(--color-gray-600);
}

/* User Form */
.user-form {
  max-width: 600px;
}

.form-header {
  margin-bottom: 24px;
}

.form-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-black);
}

.create-user-form {
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--border-radius);
  padding: 24px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--color-gray-200);
}

.cancel-button {
  padding: 8px 16px;
  border: 1px solid var(--color-gray-300);
  border-radius: var(--border-radius);
  background-color: var(--color-white);
  color: var(--color-black);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-button:hover:not(:disabled) {
  background-color: var(--color-gray-50);
}

.submit-button {
  padding: 8px 16px;
  border: none;
  border-radius: var(--border-radius);
  background-color: var(--color-black);
  color: var(--color-white);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.submit-button:hover:not(:disabled) {
  background-color: var(--color-gray-800);
}

.submit-button:disabled,
.cancel-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive */
@media (max-width: 768px) {
  .admin-header-content {
    padding: 12px 16px;
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .admin-content {
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .table-cell {
    padding: 8px 12px;
    border-bottom: 1px solid var(--color-gray-100);
  }

  .table-header .table-cell {
    display: none;
  }

  .table-row .table-cell:before {
    content: attr(data-label) ': ';
    font-weight: 600;
    color: var(--color-gray-700);
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .action-buttons {
    flex-direction: column;
  }
}
