// Tipos para la API de autenticación y administración

export enum Role {
  USER = 'USER',
  ADMIN = 'ADMIN'
}

export enum SessionType {
  WEB = 'WEB',
  MOBILE = 'MOBILE'
}

export enum DeviceType {
  ANDROID = 'ANDROID',
  IOS = 'IOS',
  WEB = 'WEB'
}

// DTOs de autenticación
export interface LoginRequest {
  identifier: string;
  password: string;
  sessionType: SessionType;
  pushToken?: string;
  deviceType?: DeviceType;
  deviceId?: string;
}

export interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  tokenType: string;
  expiresIn: number;
  user: UserProfileResponse;
}

export interface UserProfileResponse {
  id: number;
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  role: Role;
  createdAt: string;
  enabled?: boolean; // Para uso en admin
}

// DTOs de administración
export interface CreateUserRequest {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  role?: Role;
  enabled?: boolean;
}

export interface UpdateUserRequest {
  email: string;
  password?: string;
  firstName?: string;
  lastName?: string;
  role: Role;
  enabled: boolean;
}

export interface AdminUserResponse {
  id: number;
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  role: Role;
  createdAt: string;
  updatedAt: string;
  enabled: boolean;
  activeSessions: ActiveSessionInfo[];
  pushToken?: PushTokenInfo;
}

export interface ActiveSessionInfo {
  sessionType: SessionType;
  lastActivity: string;
  expiresAt: string;
}

export interface PushTokenInfo {
  deviceType: string;
  deviceId?: string;
  createdAt: string;
  lastUsed?: string;
}

export interface PagedResponse<T> {
  content: T[];
  page: number;
  size: number;
  totalElements: number;
  totalPages: number;
  first: boolean;
  last: boolean;
}

export interface UserStats {
  totalUsers: number;
  enabledUsers: number;
  disabledUsers: number;
  adminUsers: number;
  regularUsers: number;
  totalPushTokens: number;
}

// Tipos para errores de API
export interface ApiError {
  error: string;
  message?: string;
}
