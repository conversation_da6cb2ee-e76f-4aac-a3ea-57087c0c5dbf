import React from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import Login from './components/Login';
import AdminPanel from './components/AdminPanel';
import { Role } from './types/api';

const AppContent: React.FC = () => {
  const { user, isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="loading-message">
        Cargando...
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Login />;
  }

  // Verificar que el usuario sea administrador
  if (user?.role !== Role.ADMIN) {
    return (
      <div className="error-message" style={{ margin: '20px', textAlign: 'center' }}>
        Acceso denegado. Se requieren permisos de administrador.
      </div>
    );
  }

  return <AdminPanel />;
};

const App: React.FC = () => {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
};

export default App;
