import {
  LoginRequest,
  AuthResponse,
  UserProfileResponse,
  AdminUserResponse,
  CreateUserRequest,
  UpdateUserRequest,
  PagedResponse,
  UserStats,
  Role,
  ApiError
} from '../types/api';

const API_BASE_URL = 'http://localhost:8080';

class ApiServiceError extends Error {
  constructor(public status: number, message: string) {
    super(message);
    this.name = 'ApiServiceError';
  }
}

class ApiService {
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('accessToken');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new ApiServiceError(response.status, errorData.error || 'Request failed');
    }
    return response.json();
  }

  // Autenticación
  async login(loginRequest: LoginRequest): Promise<AuthResponse> {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(loginRequest)
    });
    return this.handleResponse<AuthResponse>(response);
  }

  async validateToken(): Promise<UserProfileResponse> {
    const response = await fetch(`${API_BASE_URL}/auth/validate`, {
      headers: this.getAuthHeaders()
    });
    return this.handleResponse<UserProfileResponse>(response);
  }

  async logout(sessionType: string): Promise<{ message: string }> {
    const response = await fetch(`${API_BASE_URL}/auth/logout`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ sessionType })
    });
    return this.handleResponse<{ message: string }>(response);
  }

  // Administración de usuarios
  async getUsers(params: {
    email?: string;
    enabled?: boolean;
    role?: Role;
    page?: number;
    size?: number;
    sort?: string;
  } = {}): Promise<PagedResponse<UserProfileResponse>> {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });

    const response = await fetch(`${API_BASE_URL}/admin/users?${searchParams}`, {
      headers: this.getAuthHeaders()
    });
    return this.handleResponse<PagedResponse<UserProfileResponse>>(response);
  }

  async getUserById(userId: number): Promise<AdminUserResponse> {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}`, {
      headers: this.getAuthHeaders()
    });
    return this.handleResponse<AdminUserResponse>(response);
  }

  async createUser(createUserRequest: CreateUserRequest): Promise<UserProfileResponse> {
    const response = await fetch(`${API_BASE_URL}/admin/users`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(createUserRequest)
    });
    return this.handleResponse<UserProfileResponse>(response);
  }

  async updateUser(userId: number, updateUserRequest: UpdateUserRequest): Promise<UserProfileResponse> {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(updateUserRequest)
    });
    return this.handleResponse<UserProfileResponse>(response);
  }

  async deleteUser(userId: number): Promise<{ message: string }> {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders()
    });
    return this.handleResponse<{ message: string }>(response);
  }

  async setUserEnabled(userId: number, enabled: boolean): Promise<{ message: string; enabled: boolean }> {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}/enabled`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ enabled })
    });
    return this.handleResponse<{ message: string; enabled: boolean }>(response);
  }

  async invalidateUserSessions(userId: number): Promise<{ message: string }> {
    const response = await fetch(`${API_BASE_URL}/admin/users/${userId}/invalidate-sessions`, {
      method: 'POST',
      headers: this.getAuthHeaders()
    });
    return this.handleResponse<{ message: string }>(response);
  }

  async getUserStats(): Promise<UserStats> {
    const response = await fetch(`${API_BASE_URL}/admin/users/stats`, {
      headers: this.getAuthHeaders()
    });
    return this.handleResponse<UserStats>(response);
  }
}

export const apiService = new ApiService();
export { ApiServiceError as ApiError };
